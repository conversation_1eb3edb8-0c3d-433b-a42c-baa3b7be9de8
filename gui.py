#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interfaz gráfica para el convertidor de PDF a Excel
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from pathlib import Path
from pdf_converter import PDFToExcelConverter

class PDFConverterGUI:
    """Interfaz gráfica para el convertidor de PDF a Excel"""
    
    def __init__(self):
        """Inicializar la interfaz gráfica"""
        self.root = tk.Tk()
        self.root.title("Convertidor de PDF a Excel con IA")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Variables
        self.pdf_path = tk.StringVar()
        self.excel_path = tk.StringVar()
        self.converter = PDFToExcelConverter()
        
        # Configurar estilo
        self.setup_styles()
        
        # Crear interfaz
        self.create_widgets()
        
        # Centrar ventana
        self.center_window()
    
    def setup_styles(self):
        """Configurar estilos de la interfaz"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configurar colores
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Info.TLabel', font=('Arial', 10))
    
    def create_widgets(self):
        """Crear todos los widgets de la interfaz"""
        # Frame principal
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configurar grid
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Título
        title_label = ttk.Label(
            main_frame, 
            text="🔄 Convertidor de PDF a Excel con IA", 
            style='Title.TLabel'
        )
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Sección de archivo PDF
        pdf_frame = ttk.LabelFrame(main_frame, text="Archivo PDF de entrada", padding="10")
        pdf_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        pdf_frame.columnconfigure(1, weight=1)
        
        ttk.Label(pdf_frame, text="PDF:", style='Header.TLabel').grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        pdf_entry = ttk.Entry(pdf_frame, textvariable=self.pdf_path, width=50)
        pdf_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(
            pdf_frame, 
            text="📁 Seleccionar PDF", 
            command=self.select_pdf_file
        ).grid(row=0, column=2)
        
        # Sección de archivo Excel
        excel_frame = ttk.LabelFrame(main_frame, text="Archivo Excel de salida", padding="10")
        excel_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        excel_frame.columnconfigure(1, weight=1)
        
        ttk.Label(excel_frame, text="Excel:", style='Header.TLabel').grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        excel_entry = ttk.Entry(excel_frame, textvariable=self.excel_path, width=50)
        excel_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        ttk.Button(
            excel_frame, 
            text="📁 Seleccionar Excel", 
            command=self.select_excel_file
        ).grid(row=0, column=2)
        
        # Botones de acción
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=20)
        
        self.convert_button = ttk.Button(
            button_frame, 
            text="🚀 Convertir PDF a Excel", 
            command=self.start_conversion,
            style='Accent.TButton'
        )
        self.convert_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            button_frame, 
            text="ℹ️ Información del PDF", 
            command=self.show_pdf_info
        ).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(
            button_frame, 
            text="🗑️ Limpiar", 
            command=self.clear_all
        ).pack(side=tk.LEFT)
        
        # Barra de progreso
        self.progress = ttk.Progressbar(
            main_frame, 
            mode='indeterminate',
            length=400
        )
        self.progress.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Área de log
        log_frame = ttk.LabelFrame(main_frame, text="Registro de actividad", padding="10")
        log_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(
            log_frame, 
            height=15, 
            width=70,
            wrap=tk.WORD
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Información de estado
        self.status_label = ttk.Label(
            main_frame, 
            text="Listo para convertir", 
            style='Info.TLabel'
        )
        self.status_label.grid(row=6, column=0, columnspan=3, pady=(10, 0))
    
    def center_window(self):
        """Centrar la ventana en la pantalla"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def select_pdf_file(self):
        """Seleccionar archivo PDF"""
        file_path = filedialog.askopenfilename(
            title="Seleccionar archivo PDF",
            filetypes=[("Archivos PDF", "*.pdf"), ("Todos los archivos", "*.*")]
        )
        if file_path:
            self.pdf_path.set(file_path)
            # Generar nombre de Excel automáticamente
            if not self.excel_path.get():
                excel_path = str(Path(file_path).with_suffix('.xlsx'))
                self.excel_path.set(excel_path)
            self.log_message(f"PDF seleccionado: {os.path.basename(file_path)}")
    
    def select_excel_file(self):
        """Seleccionar archivo Excel de salida"""
        file_path = filedialog.asksaveasfilename(
            title="Guardar archivo Excel como",
            defaultextension=".xlsx",
            filetypes=[("Archivos Excel", "*.xlsx"), ("Archivos Excel 97-2003", "*.xls")]
        )
        if file_path:
            self.excel_path.set(file_path)
            self.log_message(f"Excel de salida: {os.path.basename(file_path)}")
    
    def start_conversion(self):
        """Iniciar proceso de conversión en un hilo separado"""
        if not self.pdf_path.get():
            messagebox.showerror("Error", "Por favor selecciona un archivo PDF")
            return
        
        if not self.excel_path.get():
            messagebox.showerror("Error", "Por favor especifica un archivo Excel de salida")
            return
        
        # Deshabilitar botón durante la conversión
        self.convert_button.config(state='disabled')
        self.progress.start()
        self.status_label.config(text="Convirtiendo...")
        
        # Iniciar conversión en hilo separado
        thread = threading.Thread(target=self.convert_pdf)
        thread.daemon = True
        thread.start()
    
    def convert_pdf(self):
        """Convertir PDF a Excel (ejecutado en hilo separado)"""
        try:
            self.log_message("Iniciando conversión...")
            
            # Realizar conversión
            success = self.converter.convert_pdf_to_excel(
                self.pdf_path.get(), 
                self.excel_path.get()
            )
            
            if success:
                self.log_message("✅ Conversión completada exitosamente!")
                self.status_label.config(text="Conversión completada")
                messagebox.showinfo(
                    "Éxito", 
                    f"El archivo se ha convertido exitosamente:\n{self.excel_path.get()}"
                )
            else:
                self.log_message("❌ Error en la conversión")
                self.status_label.config(text="Error en la conversión")
                messagebox.showerror("Error", "No se pudo completar la conversión")
                
        except Exception as e:
            error_msg = f"Error durante la conversión: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            self.status_label.config(text="Error en la conversión")
            messagebox.showerror("Error", error_msg)
        
        finally:
            # Rehabilitar botón y detener progreso
            self.root.after(0, self.conversion_finished)
    
    def conversion_finished(self):
        """Llamado cuando termina la conversión"""
        self.convert_button.config(state='normal')
        self.progress.stop()
    
    def show_pdf_info(self):
        """Mostrar información del archivo PDF"""
        if not self.pdf_path.get():
            messagebox.showwarning("Advertencia", "Por favor selecciona un archivo PDF primero")
            return
        
        try:
            info = self.converter.get_pdf_info(self.pdf_path.get())
            if info:
                info_text = f"""Información del PDF:
                
Páginas: {info.get('páginas', 'N/A')}
Título: {info.get('título', 'N/A')}
Autor: {info.get('autor', 'N/A')}
Fecha de creación: {info.get('creado', 'N/A')}
Tamaño del archivo: {info.get('tamaño_archivo', 0):,} bytes"""
                
                self.log_message("Información del PDF obtenida")
                messagebox.showinfo("Información del PDF", info_text)
            else:
                messagebox.showerror("Error", "No se pudo obtener información del PDF")
                
        except Exception as e:
            error_msg = f"Error obteniendo información: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("Error", error_msg)
    
    def clear_all(self):
        """Limpiar todos los campos"""
        self.pdf_path.set("")
        self.excel_path.set("")
        self.log_text.delete(1.0, tk.END)
        self.status_label.config(text="Listo para convertir")
        self.log_message("Campos limpiados")
    
    def log_message(self, message: str):
        """Agregar mensaje al log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def run(self):
        """Ejecutar la aplicación"""
        self.log_message("🚀 Convertidor de PDF a Excel iniciado")
        self.log_message("Selecciona un archivo PDF para comenzar")
        self.root.mainloop()
