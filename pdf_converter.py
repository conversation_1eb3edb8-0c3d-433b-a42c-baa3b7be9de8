#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Módulo principal para la conversión de PDF a Excel
Maneja tanto PDFs digitales como escaneados usando OCR
"""

import fitz  # PyMuPDF
import pandas as pd
import cv2
import numpy as np
from PIL import Image
import pytesseract
import camelot
import io
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional
import re

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PDFToExcelConverter:
    """Clase principal para convertir PDFs a Excel"""
    
    def __init__(self):
        """Inicializar el convertidor"""
        self.supported_formats = ['.pdf']
        self.output_formats = ['.xlsx', '.xls']
        
    def convert_pdf_to_excel(self, pdf_path: str, excel_path: str) -> bool:
        """
        Convertir un archivo PDF a Excel
        
        Args:
            pdf_path: Ruta del archivo PDF
            excel_path: Ruta del archivo Excel de salida
            
        Returns:
            bool: True si la conversión fue exitosa
        """
        try:
            logger.info(f"Iniciando conversión: {pdf_path} -> {excel_path}")
            
            # Verificar que el archivo PDF existe
            if not os.path.exists(pdf_path):
                raise FileNotFoundError(f"El archivo PDF no existe: {pdf_path}")
            
            # Crear directorio de salida si no existe
            os.makedirs(os.path.dirname(excel_path), exist_ok=True)
            
            # Detectar tipo de PDF y extraer tablas
            tables = self._extract_tables_from_pdf(pdf_path)
            
            if not tables:
                logger.warning("No se encontraron tablas en el PDF")
                # Crear un Excel vacío con mensaje
                self._create_empty_excel(excel_path)
                return True
            
            # Guardar tablas en Excel
            self._save_tables_to_excel(tables, excel_path)
            
            logger.info(f"Conversión completada exitosamente: {excel_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error durante la conversión: {str(e)}")
            raise
    
    def _extract_tables_from_pdf(self, pdf_path: str) -> List[pd.DataFrame]:
        """
        Extraer tablas de un archivo PDF
        
        Args:
            pdf_path: Ruta del archivo PDF
            
        Returns:
            Lista de DataFrames con las tablas encontradas
        """
        tables = []
        
        try:
            # Intentar extraer tablas usando Camelot (para PDFs digitales)
            logger.info("Intentando extraer tablas con Camelot...")
            camelot_tables = camelot.read_pdf(pdf_path, pages='all', flavor='lattice')
            
            if camelot_tables:
                logger.info(f"Encontradas {len(camelot_tables)} tablas con Camelot")
                for table in camelot_tables:
                    if not table.df.empty:
                        tables.append(table.df)
            
        except Exception as e:
            logger.warning(f"Camelot no pudo procesar el PDF: {str(e)}")
        
        # Si no se encontraron tablas con Camelot, usar OCR
        if not tables:
            logger.info("Intentando extraer tablas con OCR...")
            ocr_tables = self._extract_tables_with_ocr(pdf_path)
            tables.extend(ocr_tables)
        
        return tables
    
    def _extract_tables_with_ocr(self, pdf_path: str) -> List[pd.DataFrame]:
        """
        Extraer tablas usando OCR para PDFs escaneados
        
        Args:
            pdf_path: Ruta del archivo PDF
            
        Returns:
            Lista de DataFrames con las tablas encontradas
        """
        tables = []
        
        try:
            # Abrir PDF con PyMuPDF
            doc = fitz.open(pdf_path)
            
            for page_num in range(len(doc)):
                page = doc[page_num]
                
                # Convertir página a imagen
                mat = fitz.Matrix(2.0, 2.0)  # Aumentar resolución
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("png")
                
                # Convertir a imagen PIL
                image = Image.open(io.BytesIO(img_data))
                
                # Convertir a OpenCV
                cv_image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
                
                # Procesar imagen para mejorar OCR
                processed_image = self._preprocess_image_for_ocr(cv_image)
                
                # Extraer texto con Tesseract
                text = pytesseract.image_to_string(processed_image, lang='spa+eng')
                
                # Buscar tablas en el texto
                page_tables = self._parse_tables_from_text(text)
                tables.extend(page_tables)
            
            doc.close()
            
        except Exception as e:
            logger.error(f"Error en OCR: {str(e)}")
        
        return tables
    
    def _preprocess_image_for_ocr(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocesar imagen para mejorar la precisión del OCR
        
        Args:
            image: Imagen en formato OpenCV
            
        Returns:
            Imagen procesada
        """
        # Convertir a escala de grises
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Aplicar filtro gaussiano para reducir ruido
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Aplicar umbralización adaptativa
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
        )
        
        # Operaciones morfológicas para limpiar la imagen
        kernel = np.ones((1, 1), np.uint8)
        cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def _parse_tables_from_text(self, text: str) -> List[pd.DataFrame]:
        """
        Parsear tablas del texto extraído por OCR
        
        Args:
            text: Texto extraído por OCR
            
        Returns:
            Lista de DataFrames con las tablas encontradas
        """
        tables = []
        lines = text.split('\n')
        
        # Buscar patrones de tabla (filas con múltiples columnas separadas por espacios/tabs)
        table_data = []
        current_table = []
        
        for line in lines:
            line = line.strip()
            if not line:
                if current_table:
                    table_data.append(current_table)
                    current_table = []
                continue
            
            # Detectar si la línea parece ser parte de una tabla
            # (contiene múltiples "palabras" separadas por espacios)
            parts = re.split(r'\s{2,}|\t', line)  # Separar por múltiples espacios o tabs
            
            if len(parts) >= 2:  # Si tiene al menos 2 columnas
                current_table.append(parts)
            else:
                if current_table:
                    table_data.append(current_table)
                    current_table = []
        
        # Agregar la última tabla si existe
        if current_table:
            table_data.append(current_table)
        
        # Convertir a DataFrames
        for table in table_data:
            if len(table) > 1:  # Al menos 2 filas
                try:
                    # Crear DataFrame
                    df = pd.DataFrame(table[1:], columns=table[0])
                    tables.append(df)
                except Exception as e:
                    logger.warning(f"Error creando DataFrame: {str(e)}")
        
        return tables
    
    def _save_tables_to_excel(self, tables: List[pd.DataFrame], excel_path: str):
        """
        Guardar tablas en archivo Excel
        
        Args:
            tables: Lista de DataFrames
            excel_path: Ruta del archivo Excel
        """
        try:
            with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
                for i, table in enumerate(tables):
                    sheet_name = f'Tabla_{i+1}'
                    table.to_excel(writer, sheet_name=sheet_name, index=False)
                    
                    # Ajustar ancho de columnas
                    worksheet = writer.sheets[sheet_name]
                    for column in worksheet.columns:
                        max_length = 0
                        column_letter = column[0].column_letter
                        for cell in column:
                            try:
                                if len(str(cell.value)) > max_length:
                                    max_length = len(str(cell.value))
                            except:
                                pass
                        adjusted_width = min(max_length + 2, 50)
                        worksheet.column_dimensions[column_letter].width = adjusted_width
                        
        except Exception as e:
            logger.error(f"Error guardando Excel: {str(e)}")
            raise
    
    def _create_empty_excel(self, excel_path: str):
        """
        Crear un archivo Excel vacío con mensaje informativo
        
        Args:
            excel_path: Ruta del archivo Excel
        """
        df = pd.DataFrame({
            'Mensaje': ['No se encontraron tablas en el PDF'],
            'Archivo': [os.path.basename(excel_path)]
        })
        
        with pd.ExcelWriter(excel_path, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Información', index=False)
    
    def get_pdf_info(self, pdf_path: str) -> Dict[str, Any]:
        """
        Obtener información del archivo PDF
        
        Args:
            pdf_path: Ruta del archivo PDF
            
        Returns:
            Diccionario con información del PDF
        """
        try:
            doc = fitz.open(pdf_path)
            info = {
                'páginas': len(doc),
                'título': doc.metadata.get('title', 'N/A'),
                'autor': doc.metadata.get('author', 'N/A'),
                'creado': doc.metadata.get('creationDate', 'N/A'),
                'tamaño_archivo': os.path.getsize(pdf_path)
            }
            doc.close()
            return info
        except Exception as e:
            logger.error(f"Error obteniendo información del PDF: {str(e)}")
            return {}
