#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Archivo de configuración para el Convertidor de PDF a Excel
"""

import os
from pathlib import Path

# Configuración de rutas
BASE_DIR = Path(__file__).parent
TEMP_DIR = BASE_DIR / "temp"
OUTPUT_DIR = BASE_DIR / "output"

# Crear directorios si no existen
TEMP_DIR.mkdir(exist_ok=True)
OUTPUT_DIR.mkdir(exist_ok=True)

# Configuración de Tesseract
TESSERACT_CONFIG = {
    'lang': 'spa+eng',  # Español e inglés
    'psm': 6,  # Modo de segmentación de página
    'oem': 3,  # Modo de motor OCR
}

# Configuración de Camelot
CAMELOT_CONFIG = {
    'flavor': 'lattice',  # Usar lattice para tablas con bordes
    'pages': 'all',
    'line_scale': 40,
}

# Configuración de OpenCV para preprocesamiento
OPENCV_CONFIG = {
    'gaussian_blur': (5, 5),
    'adaptive_threshold': {
        'max_value': 255,
        'adaptive_method': 'ADAPTIVE_THRESH_GAUSSIAN_C',
        'threshold_type': 'THRESH_BINARY',
        'block_size': 11,
        'c': 2
    }
}

# Configuración de Excel
EXCEL_CONFIG = {
    'max_column_width': 50,
    'min_column_width': 10,
    'default_column_width': 20,
}

# Configuración de logging
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'file': BASE_DIR / 'converter.log'
}

# Configuración de la GUI
GUI_CONFIG = {
    'window_size': (800, 600),
    'min_window_size': (600, 400),
    'theme': 'clam',
    'font_family': 'Arial',
    'font_sizes': {
        'title': 16,
        'header': 12,
        'normal': 10,
        'small': 8
    }
}

# Configuración de archivos soportados
SUPPORTED_FORMATS = {
    'input': ['.pdf'],
    'output': ['.xlsx', '.xls']
}

# Configuración de OCR para diferentes tipos de documentos
OCR_PRESETS = {
    'default': {
        'lang': 'spa+eng',
        'psm': 6,
        'oem': 3
    },
    'table': {
        'lang': 'spa+eng',
        'psm': 6,  # Bloque de texto uniforme
        'oem': 3
    },
    'scanned': {
        'lang': 'spa+eng',
        'psm': 3,  # Bloque de texto completamente segmentado
        'oem': 3
    }
}

# Configuración de patrones de tabla para OCR
TABLE_PATTERNS = {
    'separators': [r'\s{2,}', r'\t', r'\|\s*', r'\s*\|\s*'],
    'min_columns': 2,
    'min_rows': 2,
    'header_indicators': ['total', 'suma', 'subtotal', 'importe', 'precio', 'cantidad']
}

# Configuración de preprocesamiento de imágenes
IMAGE_PREPROCESSING = {
    'scale_factor': 2.0,  # Factor de escala para mejorar resolución
    'denoise': True,
    'sharpen': True,
    'contrast_enhancement': True
}

def get_config():
    """Obtener configuración completa"""
    return {
        'base_dir': BASE_DIR,
        'temp_dir': TEMP_DIR,
        'output_dir': OUTPUT_DIR,
        'tesseract': TESSERACT_CONFIG,
        'camelot': CAMELOT_CONFIG,
        'opencv': OPENCV_CONFIG,
        'excel': EXCEL_CONFIG,
        'logging': LOGGING_CONFIG,
        'gui': GUI_CONFIG,
        'formats': SUPPORTED_FORMATS,
        'ocr_presets': OCR_PRESETS,
        'table_patterns': TABLE_PATTERNS,
        'image_preprocessing': IMAGE_PREPROCESSING
    }

def validate_config():
    """Validar configuración"""
    errors = []
    
    # Verificar directorios
    if not BASE_DIR.exists():
        errors.append(f"Directorio base no existe: {BASE_DIR}")
    
    # Verificar Tesseract
    try:
        import pytesseract
        pytesseract.get_tesseract_version()
    except Exception as e:
        errors.append(f"Tesseract no disponible: {e}")
    
    # Verificar dependencias principales
    required_modules = ['fitz', 'camelot', 'cv2', 'pandas', 'openpyxl']
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            errors.append(f"Módulo requerido no encontrado: {module}")
    
    return errors

if __name__ == "__main__":
    # Mostrar configuración actual
    config = get_config()
    print("🔧 Configuración del Convertidor de PDF a Excel")
    print("=" * 50)
    
    for key, value in config.items():
        print(f"{key}: {value}")
    
    # Validar configuración
    print("\n🔍 Validando configuración...")
    errors = validate_config()
    
    if errors:
        print("❌ Errores encontrados:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ Configuración válida")
