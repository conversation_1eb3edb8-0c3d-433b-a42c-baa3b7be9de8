#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de prueba básico para verificar la instalación
"""

def test_imports():
    """Probar importaciones de módulos"""
    print("🧪 Probando importaciones...")
    
    try:
        import fitz
        print("✅ PyMuPDF importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando PyMuPDF: {e}")
        return False
    
    try:
        import camelot
        print("✅ Camelot importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando Camelot: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando OpenCV: {e}")
        return False
    
    try:
        import pandas
        print("✅ Pandas importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando Pandas: {e}")
        return False
    
    try:
        import openpyxl
        print("✅ OpenPyXL importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando OpenPyXL: {e}")
        return False
    
    try:
        import pytesseract
        print("✅ Pytesseract importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando Pytesseract: {e}")
        return False
    
    return True

def test_tesseract():
    """Probar Tesseract OCR"""
    print("\n🔍 Probando Tesseract OCR...")
    try:
        import pytesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract versión {version} funcionando")
        return True
    except Exception as e:
        print(f"❌ Error con Tesseract: {e}")
        print("💡 Tesseract no está instalado. Necesario para PDFs escaneados.")
        return False

def test_pdf_converter():
    """Probar el convertidor de PDF"""
    print("\n🔄 Probando convertidor de PDF...")
    try:
        from pdf_converter import PDFToExcelConverter
        converter = PDFToExcelConverter()
        print("✅ Convertidor de PDF inicializado correctamente")
        return True
    except Exception as e:
        print(f"❌ Error inicializando convertidor: {e}")
        return False

def test_gui():
    """Probar la interfaz gráfica"""
    print("\n🖥️ Probando interfaz gráfica...")
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Ocultar ventana
        root.destroy()
        print("✅ Tkinter funcionando correctamente")
        return True
    except Exception as e:
        print(f"❌ Error con Tkinter: {e}")
        return False

def main():
    """Función principal de pruebas"""
    print("🚀 Pruebas de instalación del Convertidor de PDF a Excel")
    print("=" * 60)
    
    tests = [
        ("Importaciones", test_imports),
        ("Tesseract OCR", test_tesseract),
        ("Convertidor PDF", test_pdf_converter),
        ("Interfaz Gráfica", test_gui)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Error inesperado en {test_name}: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 Resultados: {passed}/{total} pruebas pasaron")
    
    if passed == total:
        print("🎉 ¡Todas las pruebas pasaron! La instalación está completa.")
        print("\n💡 Para usar la aplicación:")
        print("   python main.py --gui")
    else:
        print("⚠️  Algunas pruebas fallaron. Revisa los errores arriba.")
        if passed >= 3:  # Si al menos las importaciones básicas funcionan
            print("\n💡 La aplicación básica debería funcionar para PDFs digitales.")
            print("   Para PDFs escaneados, instala Tesseract OCR.")

if __name__ == "__main__":
    main()
