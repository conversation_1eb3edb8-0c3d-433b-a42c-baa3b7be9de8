#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Ejemplo de uso del Convertidor de PDF a Excel
Demuestra cómo usar la librería programáticamente
"""

import os
import sys
from pathlib import Path
from pdf_converter import PDFToExcelConverter

def ejemplo_basico():
    """Ejemplo básico de conversión"""
    print("🔄 Ejemplo básico de conversión")
    print("=" * 40)
    
    # Crear instancia del convertidor
    converter = PDFToExcelConverter()
    
    # Ruta del PDF (cambiar por tu archivo)
    pdf_path = "ejemplo.pdf"
    excel_path = "ejemplo_salida.xlsx"
    
    # Verificar que el PDF existe
    if not os.path.exists(pdf_path):
        print(f"❌ Archivo PDF no encontrado: {pdf_path}")
        print("💡 Coloca un archivo PDF llamado 'ejemplo.pdf' en el directorio actual")
        return False
    
    try:
        # Convertir PDF a Excel
        print(f"📄 Convirtiendo: {pdf_path} -> {excel_path}")
        success = converter.convert_pdf_to_excel(pdf_path, excel_path)
        
        if success:
            print("✅ Conversión exitosa!")
            
            # Mostrar información del archivo generado
            if os.path.exists(excel_path):
                size = os.path.getsize(excel_path)
                print(f"📊 Archivo Excel generado: {excel_path} ({size:,} bytes)")
            
            return True
        else:
            print("❌ Error en la conversión")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

def ejemplo_informacion_pdf():
    """Ejemplo de obtención de información del PDF"""
    print("\n📋 Ejemplo de información del PDF")
    print("=" * 40)
    
    converter = PDFToExcelConverter()
    pdf_path = "ejemplo.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"❌ Archivo PDF no encontrado: {pdf_path}")
        return
    
    try:
        info = converter.get_pdf_info(pdf_path)
        
        if info:
            print("📄 Información del PDF:")
            for key, value in info.items():
                print(f"  {key}: {value}")
        else:
            print("❌ No se pudo obtener información del PDF")
            
    except Exception as e:
        print(f"❌ Error obteniendo información: {str(e)}")

def ejemplo_procesamiento_lote():
    """Ejemplo de procesamiento por lotes"""
    print("\n📁 Ejemplo de procesamiento por lotes")
    print("=" * 40)
    
    converter = PDFToExcelConverter()
    
    # Buscar todos los PDFs en el directorio actual
    pdf_files = list(Path(".").glob("*.pdf"))
    
    if not pdf_files:
        print("❌ No se encontraron archivos PDF en el directorio actual")
        return
    
    print(f"📄 Encontrados {len(pdf_files)} archivos PDF:")
    for pdf_file in pdf_files:
        print(f"  - {pdf_file}")
    
    # Procesar cada archivo
    exitosos = 0
    fallidos = 0
    
    for pdf_file in pdf_files:
        excel_file = pdf_file.with_suffix('.xlsx')
        
        try:
            print(f"\n🔄 Procesando: {pdf_file}")
            success = converter.convert_pdf_to_excel(str(pdf_file), str(excel_file))
            
            if success:
                print(f"✅ Completado: {excel_file}")
                exitosos += 1
            else:
                print(f"❌ Falló: {pdf_file}")
                fallidos += 1
                
        except Exception as e:
            print(f"❌ Error procesando {pdf_file}: {str(e)}")
            fallidos += 1
    
    print(f"\n📊 Resumen del procesamiento:")
    print(f"  ✅ Exitosos: {exitosos}")
    print(f"  ❌ Fallidos: {fallidos}")
    print(f"  📄 Total: {len(pdf_files)}")

def ejemplo_personalizado():
    """Ejemplo con configuración personalizada"""
    print("\n⚙️ Ejemplo con configuración personalizada")
    print("=" * 40)
    
    # Aquí podrías mostrar cómo usar configuraciones específicas
    # para diferentes tipos de documentos
    
    print("💡 Para configuraciones personalizadas, modifica el archivo config.py")
    print("   o usa los parámetros específicos de cada función")

def main():
    """Función principal de ejemplos"""
    print("🚀 Ejemplos de uso del Convertidor de PDF a Excel")
    print("=" * 60)
    
    # Ejecutar ejemplos
    ejemplos = [
        ("Ejemplo básico", ejemplo_basico),
        ("Información del PDF", ejemplo_informacion_pdf),
        ("Procesamiento por lotes", ejemplo_procesamiento_lote),
        ("Configuración personalizada", ejemplo_personalizado)
    ]
    
    for nombre, funcion in ejemplos:
        try:
            print(f"\n{'='*60}")
            funcion()
        except KeyboardInterrupt:
            print("\n\n⏹️ Ejemplos interrumpidos por el usuario")
            break
        except Exception as e:
            print(f"\n❌ Error en {nombre}: {str(e)}")
    
    print(f"\n{'='*60}")
    print("🎉 Ejemplos completados!")
    print("\n💡 Para más información, consulta el README.md")

if __name__ == "__main__":
    main()
