#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de instalación para el Convertidor de PDF a Excel
Instala automáticamente las dependencias y verifica la configuración
"""

import subprocess
import sys
import os
import platform
from pathlib import Path

def run_command(command, description):
    """Ejecutar comando y mostrar resultado"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completado")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error en {description}: {e.stderr}")
        return False

def check_python_version():
    """Verificar versión de Python"""
    print("🐍 Verificando versión de Python...")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - OK")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Se requiere Python 3.8+")
        return False

def install_requirements():
    """Instalar dependencias de requirements.txt"""
    if not os.path.exists("requirements.txt"):
        print("❌ Archivo requirements.txt no encontrado")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Instalando dependencias de Python"
    )

def check_tesseract():
    """Verificar si Tesseract está instalado"""
    print("🔍 Verificando instalación de Tesseract...")
    try:
        result = subprocess.run(["tesseract", "--version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Tesseract está instalado")
            return True
        else:
            print("❌ Tesseract no está instalado o no está en el PATH")
            return False
    except FileNotFoundError:
        print("❌ Tesseract no está instalado")
        return False

def install_tesseract_instructions():
    """Mostrar instrucciones para instalar Tesseract"""
    system = platform.system().lower()
    
    print("\n📋 Instrucciones para instalar Tesseract:")
    print("=" * 50)
    
    if system == "windows":
        print("""
Windows:
1. Descargar desde: https://github.com/UB-Mannheim/tesseract/wiki
2. Instalar el ejecutable
3. Agregar al PATH del sistema
4. O usar chocolatey: choco install tesseract
        """)
    elif system == "darwin":  # macOS
        print("""
macOS:
brew install tesseract
        """)
    elif system == "linux":
        print("""
Ubuntu/Debian:
sudo apt-get install tesseract-ocr tesseract-ocr-spa

CentOS/RHEL:
sudo yum install tesseract tesseract-langpack-spa
        """)
    else:
        print("""
Sistema no reconocido. Por favor instala Tesseract manualmente:
https://github.com/tesseract-ocr/tesseract
        """)

def create_test_script():
    """Crear script de prueba"""
    test_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
Script de prueba para verificar la instalación
\"\"\"

def test_imports():
    \"\"\"Probar importaciones de módulos\"\"\"
    try:
        import fitz
        print("✅ PyMuPDF importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando PyMuPDF: {e}")
        return False
    
    try:
        import camelot
        print("✅ Camelot importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando Camelot: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando OpenCV: {e}")
        return False
    
    try:
        import pytesseract
        print("✅ Tesseract importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando Tesseract: {e}")
        return False
    
    try:
        import pandas
        print("✅ Pandas importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando Pandas: {e}")
        return False
    
    try:
        import openpyxl
        print("✅ OpenPyXL importado correctamente")
    except ImportError as e:
        print(f"❌ Error importando OpenPyXL: {e}")
        return False
    
    return True

def test_tesseract():
    \"\"\"Probar Tesseract OCR\"\"\"
    try:
        import pytesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract versión {version} funcionando")
        return True
    except Exception as e:
        print(f"❌ Error con Tesseract: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Ejecutando pruebas de instalación...")
    print("=" * 40)
    
    imports_ok = test_imports()
    tesseract_ok = test_tesseract()
    
    print("=" * 40)
    if imports_ok and tesseract_ok:
        print("🎉 ¡Todas las pruebas pasaron! La instalación está completa.")
    else:
        print("⚠️  Algunas pruebas fallaron. Revisa los errores arriba.")
"""
    
    with open("test_installation.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("✅ Script de prueba creado: test_installation.py")

def main():
    """Función principal de instalación"""
    print("🚀 Instalador del Convertidor de PDF a Excel")
    print("=" * 50)
    
    # Verificar Python
    if not check_python_version():
        print("\n❌ Instalación cancelada: Versión de Python incompatible")
        return False
    
    # Instalar dependencias
    if not install_requirements():
        print("\n❌ Instalación cancelada: Error instalando dependencias")
        return False
    
    # Verificar Tesseract
    tesseract_ok = check_tesseract()
    if not tesseract_ok:
        install_tesseract_instructions()
        print("\n⚠️  Instala Tesseract y ejecuta el script de prueba")
    
    # Crear script de prueba
    create_test_script()
    
    print("\n" + "=" * 50)
    print("🎉 Instalación completada!")
    print("\n📋 Próximos pasos:")
    print("1. Si Tesseract no está instalado, instálalo siguiendo las instrucciones")
    print("2. Ejecuta: python test_installation.py")
    print("3. Ejecuta: python main.py --gui")
    print("\n¡Disfruta convirtiendo PDFs a Excel! 🎉")
    
    return True

if __name__ == "__main__":
    main()
