# 🔄 Convertidor de PDF a Excel con IA

Una aplicación de Python que convierte archivos PDF (tanto digitales como escaneados) a archivos Excel con formato de tablas utilizando inteligencia artificial y OCR.

## ✨ Características

- **Conversión de PDFs digitales**: Extrae tablas directamente de PDFs nativos usando Camelot
- **Conversión de PDFs escaneados**: Utiliza OCR (Tesseract) para procesar imágenes escaneadas
- **Interfaz gráfica intuitiva**: GUI fácil de usar con tkinter
- **Modo línea de comandos**: Para automatización y scripts
- **Detección automática**: Identifica automáticamente el tipo de PDF y aplica el método apropiado
- **Múltiples tablas**: Extrae y organiza múltiples tablas en hojas separadas
- **Formato Excel optimizado**: Ajusta automáticamente el ancho de columnas

## 🚀 Instalación

### Requisitos previos

1. **Python 3.8 o superior**
2. **Tesseract OCR** (para procesar PDFs escaneados)

#### Instalación de Tesseract:

**Windows:**
```bash
# Descargar e instalar desde: https://github.com/UB-Mannheim/tesseract/wiki
# O usar chocolatey:
choco install tesseract
```

**macOS:**
```bash
brew install tesseract
```

**Ubuntu/Debian:**
```bash
sudo apt-get install tesseract-ocr tesseract-ocr-spa
```

### Instalación del proyecto

1. **Clonar o descargar el proyecto:**
```bash
git clone <url-del-repositorio>
cd CONVERTIDOR-DE-PDF-A-EXCEL
```

2. **Crear entorno virtual (recomendado):**
```bash
python -m venv venv

# Windows:
venv\\Scripts\\activate

# macOS/Linux:
source venv/bin/activate
```

3. **Instalar dependencias:**
```bash
pip install -r requirements.txt
```

## 📖 Uso

### Interfaz Gráfica (Recomendado)

```bash
python main.py --gui
```

O simplemente:
```bash
python main.py
```

**Pasos:**
1. Selecciona tu archivo PDF usando el botón "📁 Seleccionar PDF"
2. Especifica la ruta del archivo Excel de salida (opcional)
3. Haz clic en "🚀 Convertir PDF a Excel"
4. Espera a que se complete la conversión
5. ¡Listo! Tu archivo Excel estará listo

### Línea de Comandos

```bash
# Conversión básica
python main.py --pdf archivo.pdf --output archivo.xlsx

# Solo especificar PDF (genera Excel automáticamente)
python main.py --pdf archivo.pdf

# Ver información del PDF
python main.py --pdf archivo.pdf --info
```

## 🛠️ Estructura del Proyecto

```
CONVERTIDOR-DE-PDF-A-EXCEL/
├── main.py              # Punto de entrada principal
├── pdf_converter.py     # Lógica de conversión
├── gui.py              # Interfaz gráfica
├── requirements.txt    # Dependencias
└── README.md          # Este archivo
```

## 🔧 Funcionamiento Técnico

### Para PDFs Digitales:
1. Utiliza **Camelot** para extraer tablas directamente del PDF
2. Convierte las tablas a DataFrames de pandas
3. Exporta a Excel con formato optimizado

### Para PDFs Escaneados:
1. Convierte cada página del PDF a imagen usando **PyMuPDF**
2. Preprocesa las imágenes para mejorar la calidad del OCR
3. Utiliza **Tesseract** para extraer texto
4. Parsea el texto para identificar patrones de tabla
5. Convierte a DataFrames y exporta a Excel

## 📋 Dependencias Principales

- **PyMuPDF**: Procesamiento de PDFs
- **Camelot**: Extracción de tablas de PDFs digitales
- **OpenCV**: Procesamiento de imágenes
- **Tesseract**: Reconocimiento óptico de caracteres
- **Pandas**: Manipulación de datos
- **OpenPyXL**: Escritura de archivos Excel
- **Tkinter**: Interfaz gráfica

## ⚠️ Limitaciones

- La precisión del OCR depende de la calidad de la imagen escaneada
- PDFs con tablas muy complejas pueden requerir ajustes manuales
- El procesamiento de PDFs grandes puede tomar tiempo
- Se requiere Tesseract instalado para PDFs escaneados

## 🐛 Solución de Problemas

### Error: "Tesseract not found"
- Instala Tesseract OCR siguiendo las instrucciones de instalación
- Asegúrate de que esté en el PATH del sistema

### Error: "No module named 'camelot'"
- Instala las dependencias: `pip install -r requirements.txt`

### Error: "No tables found"
- Verifica que el PDF contenga tablas visibles
- Para PDFs escaneados, asegúrate de que la calidad de imagen sea buena

### Conversión lenta
- Los PDFs grandes o con muchas páginas tardan más
- Considera dividir PDFs muy grandes en archivos más pequeños

## 📝 Ejemplos de Uso

### Convertir un PDF de factura:
```bash
python main.py --pdf factura.pdf --output factura.xlsx
```

### Procesar múltiples archivos (script):
```python
import os
from pdf_converter import PDFToExcelConverter

converter = PDFToExcelConverter()
pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]

for pdf_file in pdf_files:
    excel_file = pdf_file.replace('.pdf', '.xlsx')
    converter.convert_pdf_to_excel(pdf_file, excel_file)
    print(f"Convertido: {pdf_file} -> {excel_file}")
```

## 🤝 Contribuciones

¡Las contribuciones son bienvenidas! Si encuentras algún problema o tienes ideas para mejorar:

1. Reporta bugs en la sección de Issues
2. Propón nuevas características
3. Envía pull requests con mejoras

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo LICENSE para más detalles.

## 📞 Soporte

Si tienes problemas o preguntas:
- Revisa la sección de solución de problemas
- Crea un issue en el repositorio
- Contacta al desarrollador

---

**¡Disfruta convirtiendo tus PDFs a Excel! 🎉**
