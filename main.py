#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Convertidor de PDF a Excel con IA
Convierte archivos PDF (digitales y escaneados) a archivos Excel con formato de tablas
"""

import os
import sys
import argparse
from pathlib import Path
from pdf_converter import PDFToExcelConverter
from gui import PDFConverterGUI

def main():
    """Función principal del programa"""
    parser = argparse.ArgumentParser(description='Convertidor de PDF a Excel con IA')
    parser.add_argument('--pdf', type=str, help='Ruta del archivo PDF a convertir')
    parser.add_argument('--output', type=str, help='Ruta del archivo Excel de salida')
    parser.add_argument('--gui', action='store_true', help='Abrir interfaz gráfica')
    
    args = parser.parse_args()
    
    if args.gui or (not args.pdf and not args.output):
        # Abrir interfaz gráfica
        app = PDFConverterGUI()
        app.run()
    else:
        # Modo línea de comandos
        if not args.pdf:
            print("Error: Debe especificar un archivo PDF con --pdf")
            sys.exit(1)
        
        if not args.output:
            # Generar nombre de salida automáticamente
            pdf_path = Path(args.pdf)
            args.output = pdf_path.with_suffix('.xlsx')
        
        converter = PDFToExcelConverter()
        try:
            converter.convert_pdf_to_excel(args.pdf, args.output)
            print(f"✅ Conversión completada: {args.pdf} -> {args.output}")
        except Exception as e:
            print(f"❌ Error durante la conversión: {str(e)}")
            sys.exit(1)

if __name__ == "__main__":
    main()
